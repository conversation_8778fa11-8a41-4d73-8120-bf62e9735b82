#!/usr/bin/env python3
"""Test script to verify the new Excel format is working correctly."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path.cwd() / "src"))

from models.entities import ProcessingResult, Machinery, SparePart, MaintenanceJob
from exporters.excel_exporter import ExcelExporter
from datetime import datetime

def test_new_format():
    """Test the new Excel format with sample data."""
    
    # Create sample data
    machinery = [
        Machinery(
            name="Tank Cleaning Machine",
            type="Cleaning Equipment",
            description="High pressure tank cleaning system",
            manufacturer="Butterworth",
            model="TCM-500",
            confidence_score=0.9
        )
    ]
    
    maintenance_jobs = [
        MaintenanceJob(
            name="Tank Cleaning Machine Inspection",
            equipment_name="Tank Cleaning Machine",
            job_action="Inspection",
            frequency=6,
            frequency_type="Months",
            job_description="Visual inspection of all components, check for wear and damage",
            confidence_score=0.8
        ),
        MaintenanceJob(
            name="Tank Cleaning Machine Overhaul",
            equipment_name="Tank Cleaning Machine",
            job_action="Overhaul",
            frequency=24,
            frequency_type="Months",
            job_description="Complete disassembly, inspection, and replacement of worn parts",
            confidence_score=0.8
        )
    ]
    
    spare_parts = [
        SparePart(
            name="High Pressure Nozzle",
            part_number="TCM-NOZZLE-001",
            drawing_number="DWG-001",
            position_number="1",
            material="Stainless Steel",
            units="EA",
            quantity=2,
            spare_part_title="Tank Cleaning Machine Spare Parts",
            confidence_score=0.9
        ),
        SparePart(
            name="Pressure Hose",
            part_number="TCM-HOSE-002",
            drawing_number="DWG-002",
            position_number="2",
            material="Rubber",
            units="M",
            quantity=10,
            spare_part_title="Tank Cleaning Machine Spare Parts",
            confidence_score=0.9
        )
    ]
    
    # Create processing result
    result = ProcessingResult(
        document_id="test-001",
        document_name="Tank Cleaning Machine Manual",
        total_pages=50,
        sections_processed=10,
        machinery=machinery,
        maintenance_jobs=maintenance_jobs,
        spare_parts=spare_parts
    )
    
    # Export to Excel
    exporter = ExcelExporter()
    output_path = exporter.export_processing_result(result)
    
    print(f"✅ Test Excel file created: {output_path}")
    print(f"📊 Data exported:")
    print(f"   - {len(machinery)} machinery items")
    print(f"   - {len(maintenance_jobs)} maintenance jobs")
    print(f"   - {len(spare_parts)} spare parts")
    
    return output_path

if __name__ == "__main__":
    test_new_format()
