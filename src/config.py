"""Configuration settings for the PDF Understanding Tool."""

import os
from pathlib import Path
from typing import Dict, List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings."""
    
    # API Keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    aws_access_key_id: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field(default="us-east-1", env="AWS_REGION")
    google_credentials_path: Optional[str] = Field(default=None, env="GOOGLE_APPLICATION_CREDENTIALS")
    
    # Directories
    base_dir: Path = Path(__file__).parent.parent
    uploads_dir: Path = base_dir / "uploads"
    outputs_dir: Path = base_dir / "outputs"
    templates_dir: Path = base_dir / "templates"
    temp_dir: Path = base_dir / "temp"
    
    # OCR Settings
    ocr_provider: str = Field(default="tesseract", description="tesseract, textract, or vision")
    tesseract_cmd: Optional[str] = Field(default=None, env="TESSERACT_CMD")
    ocr_languages: List[str] = Field(default=["eng"], description="OCR languages")
    
    # PDF Processing
    max_file_size_mb: int = Field(default=100, description="Maximum PDF file size in MB")
    chunk_size: int = Field(default=1000, description="Text chunk size for processing")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")
    
    # AI Model Settings
    llm_model: str = Field(default="gpt-3.5-turbo", description="LLM model to use")
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2")
    temperature: float = Field(default=0.1, description="LLM temperature")
    max_tokens: int = Field(default=2000, description="Maximum tokens per response")
    
    # Entity Extraction
    machinery_keywords: List[str] = Field(
        default=[
            "engine", "motor", "pump", "compressor", "generator", "turbine",
            "boiler", "heat exchanger", "valve", "propeller", "rudder",
            "winch", "crane", "anchor", "steering gear", "thruster"
        ]
    )
    
    subcomponent_keywords: List[str] = Field(
        default=[
            "bearing", "seal", "gasket", "coupling", "shaft", "impeller",
            "rotor", "stator", "cylinder", "piston", "connecting rod",
            "crankshaft", "camshaft", "filter", "cooler", "sensor"
        ]
    )
    
    spare_part_keywords: List[str] = Field(
        default=[
            "bolt", "nut", "washer", "screw", "spring", "o-ring",
            "bushing", "pin", "key", "plug", "cap", "cover",
            "element", "cartridge", "membrane", "diaphragm"
        ]
    )
    
    # Export Settings
    excel_format: str = Field(default="xlsx", description="Excel format")
    include_page_references: bool = Field(default=True)
    include_confidence_scores: bool = Field(default=True)
    
    # Web Interface
    host: str = Field(default="0.0.0.0")
    port: int = Field(default=8000)
    debug: bool = Field(default=False)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create directories if they don't exist
        for dir_path in [self.uploads_dir, self.outputs_dir, self.templates_dir, self.temp_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()


# Entity extraction prompts
ENTITY_EXTRACTION_PROMPTS = {
    "machinery": """
    You are an expert in marine engineering and ship machinery. Analyze the following text and extract information about machinery/equipment.
    
    For each piece of machinery found, extract:
    - Name: The specific name or model of the machinery
    - Type: Category (engine, pump, compressor, etc.)
    - Description: Brief description of function/purpose
    - Specifications: Technical specifications if available
    - Page: Page number where found
    
    Text to analyze:
    {text}
    
    Return the results in JSON format as a list of machinery objects.
    """,
    
    "subcomponents": """
    You are an expert in marine engineering. Analyze the following text and extract information about subcomponents/assemblies.
    
    For each subcomponent found, extract:
    - Name: Component name
    - Parent_Machinery: The main machinery this belongs to
    - Function: What this component does
    - Parameters: Technical parameters (size, pressure, material, etc.)
    - Page: Page number where found
    
    Text to analyze:
    {text}
    
    Return the results in JSON format as a list of subcomponent objects.
    """,
    
    "spare_parts": """
    You are an expert in marine spare parts and maintenance. Analyze the following text and extract spare parts information.
    
    For each spare part found, extract:
    - Name: Part name
    - Part_Number: Manufacturer part number if available
    - Subcomponent: Which subcomponent this part belongs to
    - Material: Material specification
    - Size: Dimensions or size specification
    - Operating_Pressure: Pressure rating if applicable
    - Manufacturer: Manufacturer name if available
    - Page: Page number where found
    
    Text to analyze:
    {text}
    
    Return the results in JSON format as a list of spare part objects.
    """
}
