"""FastAPI application setup."""

from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path
import uvicorn
from loguru import logger

from .routes import router
from ..config import settings


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title="PDF Understanding Tool for Shipping Industry",
        description="AI-powered tool for extracting machinery, subcomponents, and spare parts from PDF manuals",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routes
    app.include_router(router)
    
    # Setup static files and templates
    static_dir = Path(__file__).parent / "static"
    templates_dir = Path(__file__).parent / "templates"
    
    # Create directories if they don't exist
    static_dir.mkdir(exist_ok=True)
    templates_dir.mkdir(exist_ok=True)
    
    # Mount static files
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # Setup templates
    app.state.templates = Jinja2Templates(directory=str(templates_dir))
    
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info("PDF Understanding Tool starting up...")
        
        # Create necessary directories
        settings.uploads_dir.mkdir(exist_ok=True)
        settings.outputs_dir.mkdir(exist_ok=True)
        settings.temp_dir.mkdir(exist_ok=True)
        
        # Initialize default templates
        try:
            from ..exporters.template_manager import TemplateManager
            template_manager = TemplateManager()
            template_manager.create_default_templates()
            logger.info("Default templates initialized")
        except Exception as e:
            logger.warning(f"Could not initialize default templates: {str(e)}")
        
        logger.info("Application startup completed")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info("PDF Understanding Tool shutting down...")
        
        # Cleanup temporary files
        try:
            import shutil
            if settings.temp_dir.exists():
                shutil.rmtree(settings.temp_dir)
                settings.temp_dir.mkdir(exist_ok=True)
            logger.info("Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Error cleaning up temporary files: {str(e)}")
        
        logger.info("Application shutdown completed")
    
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """Handle 404 errors."""
        return app.state.templates.TemplateResponse(
            "error.html",
            {"request": request, "error_code": 404, "error_message": "Page not found"}
        )
    
    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        """Handle 500 errors."""
        logger.error(f"Internal server error: {str(exc)}")
        return app.state.templates.TemplateResponse(
            "error.html",
            {"request": request, "error_code": 500, "error_message": "Internal server error"}
        )
    
    return app


def run_app():
    """Run the FastAPI application."""
    app = create_app()
    
    logger.info(f"Starting server on {settings.host}:{settings.port}")
    
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        debug=settings.debug,
        reload=settings.debug
    )


if __name__ == "__main__":
    run_app()
